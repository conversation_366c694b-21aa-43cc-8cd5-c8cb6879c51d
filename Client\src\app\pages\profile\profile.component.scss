@use "sass:color";

.profile-container {
  max-width: 1040px;
  margin: 0 auto;
  padding: 20px;

  border-radius: 8px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }

  .profile-actions {
    display: flex;
    gap: 10px;
  }

  .edit-actions {
    display: flex;
    gap: 10px;
  }

  .p-button-danger {
    height: 45px !important;
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;

    &:hover {
      background-color: #c82333 !important;
      border-color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }
  .p-button-success {
    border-radius: 12px !important;
    height: 45px !important;
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #28a745 !important;

    &:hover {
      background-color: #218838 !important;
      border-color: #1e7e34 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2) !important;
    }
  }
  .p-button-secondary {
    border-radius: 12px !important;
    height: 45px !important;
    background-color: #6c757d !important;
    color: white !important;
    border: 1px solid #6c757d !important;

    &:hover {
      background-color: #5a6268 !important;
      border-color: #545b62 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.2) !important;
    }
  }

  .edit-btn,
  .save-btn,
  .cancel-btn,
  .change-password-btn {
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;

    i {
      font-size: 16px;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }

  .edit-btn {
    background-color: #dc3545;
    color: white;

    &:hover {
      background-color: color.adjust(#dc3545, $lightness: -10%);
    }

    &:disabled {
      background-color: #e9a8ae;
    }
  }

  .save-btn {
    background-color: #28a745;
    color: white;

    &:hover {
      background-color: color.adjust(#28a745, $lightness: -10%);
    }

    &:disabled {
      background-color: #a8d4ae;
    }
  }

  .cancel-btn {
    background-color: #6c757d;
    color: white;

    &:hover {
      background-color: color.adjust(#6c757d, $lightness: -10%);
    }

    &:disabled {
      background-color: #b8bcc0;
    }
  }

  .change-password-btn {
    background-color: #f8f9fa;
    color: #dc3545;
    border: 1px solid #dc3545;

    &:hover {
      background-color: #f1f1f1;
    }

    &:disabled {
      border-color: #e9a8ae;
      color: #e9a8ae;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  background-color: white;
  border-radius: 8px;
  margin-bottom: 20px;

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  p {
    margin-top: 15px;
    color: #6c757d;
  }
}

.profile-content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  position: relative;
}

.profile-avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #f8f9fa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.profile-row {
  display: flex;
  gap: 24px;

  &.full-width {
    flex-direction: column;
  }
}

.profile-field {
  flex: 1;

  label {
    display: block;
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
  }

  .field-value {
    font-size: 16px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    min-height: 44px;
    display: flex;
    align-items: center;

    &.description {
      min-height: 100px;
      align-items: flex-start;
      line-height: 1.5;
    }
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #dc3545;

  &.active {
    background-color: #28a745;
  }
}

.social-media-title {
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0;
}

.social-input {
  background-color: #f8f9fa;
  border-radius: 4px;
  height: 44px;
  border: 1px solid #ced4da;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

// Validation error styles
.validation-errors {
  .alert {
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    background-color: #f8d7da;
    color: #721c24;

    h6 {
      font-weight: 600;
      margin-bottom: 8px;
    }

    ul {
      list-style-type: disc;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
      }
    }
  }
}

// Form input styles for edit mode
.profile-field {
  input[pInputText],
  textarea[pInputTextarea] {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 14px;
    transition:
      border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;

    &:focus {
      border-color: #dc3545;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    &.ng-invalid.ng-dirty {
      border-color: #dc3545;
    }

    &::placeholder {
      color: #6c757d;
      opacity: 1;
    }
  }

  textarea[pInputTextarea] {
    resize: vertical;
    min-height: 100px;
  }

  .p-error {
    color: #dc3545;
    font-size: 12px;
    font-weight: 400;
  }

  .text-muted {
    color: #6c757d !important;
    font-size: 12px;
    font-style: italic;
  }
}

// Required field indicator
.text-danger {
  color: #dc3545 !important;
}

@media (max-width: 768px) {
  .profile-row {
    flex-direction: column;
  }

  .profile-header {
    .edit-actions {
      flex-direction: column;
      gap: 8px;
    }
  }
}
